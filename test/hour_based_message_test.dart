import 'package:flutter_test/flutter_test.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';

void main() {
  group('Hour-based Message Tests', () {
    test('isCurrentHourOdd returns correct values', () {
      // This test will depend on the current time, so we'll test the logic
      final currentHour = DateTime.now().hour;
      final expectedResult = currentHour % 2 == 1;
      
      expect(isCurrentHourOdd(), equals(expectedResult));
    });

    test('isCurrentHourOdd logic works for different hours', () {
      // Test the logic directly
      for (int hour = 0; hour < 24; hour++) {
        final isOdd = hour % 2 == 1;
        final expectedOddHours = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23];
        final expectedEvenHours = [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22];
        
        if (isOdd) {
          expect(expectedOddHours.contains(hour), isTrue, 
            reason: 'Hour $hour should be odd');
        } else {
          expect(expectedEvenHours.contains(hour), isTrue,
            reason: 'Hour $hour should be even');
        }
      }
    });
  });
}
