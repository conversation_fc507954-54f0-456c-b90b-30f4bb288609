import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/storage/token_manager.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/explore/presentation/pages/explore_home_page.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/bottom_navbar/bottom_navbar_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/pages/normal_feed_page.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/blocs/notification/notification_cubit.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/blocs/notification/notification_state.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/hour_based_message_utils.dart';
import '../../../../locator.dart';
import '../../../profile/presentation/pages/profile_page.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({
    super.key,
    required this.index,
  });

  final int index;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late PageController homePageController;

  late int _selectedIndex;
  int? mode = 0;
  int? counts = 0;
  int selectedItem = -1;

  late List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.index;
    context.read<NotificationCubit>().getNotificationCount();
    _setStatusBar();
    sl<MixpanelService>().trackScreenView(
      'Home Page',
      properties: {'Code': 'screen_view.home_page'},
    );

    homePageController = PageController(initialPage: widget.index);
    info('widget.index1----${widget.index}');
    _pages = <Widget>[
      NormalFeedPage(homePageController: homePageController),
      const ExplorePage(),
      const ProfilePage(),
    ];
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setStatusBar();
      precacheBottomNavIcons();
      // Check if we should show message sheet from notification permission page navigation
      HourBasedMessageUtils.showMessageSheetForNotificationNavigation(context);
    });
  }

  @override
  void didUpdateWidget(HomePage oldWidget) {
    super.didUpdateWidget(oldWidget);
    info('widget.index2----${widget.index}');
    if (oldWidget.index != widget.index) {
      setState(() {
        _selectedIndex = widget.index;
      });
      if (homePageController.hasClients) {
        homePageController.jumpToPage(widget.index);
      }
    }
  }

  void _setReceivedIndex(int? index) {
    if (index == null || !mounted) return;

    setState(() {
      _selectedIndex = index;
    });

    if (homePageController.hasClients) {
      homePageController.jumpToPage(index);
    }
  }

  void precacheBottomNavIcons() {
    precacheImage(const AssetImage(AppAssets.homefilled), context);
    precacheImage(const AssetImage(AppAssets.homeicon), context);
    precacheImage(const AssetImage(AppAssets.explorehomeicon), context);
    precacheImage(const AssetImage(AppAssets.explorefilled), context);
    precacheImage(const AssetImage(AppAssets.profilefilled), context);
    precacheImage(const AssetImage(AppAssets.profile), context);
  }

  @override
  void dispose() {
    homePageController.dispose();
    super.dispose();
  }

  void _setStatusBar() {
    // SystemChrome.setSystemUIOverlayStyle(
    //   const SystemUiOverlayStyle(
    //     statusBarColor: Colors.white,
    //     statusBarBrightness: Brightness.light,
    //     statusBarIconBrightness: Brightness.dark,
    //   ),
    // );
  }

  token() async {
    var a = await sl<TokenManager>().getAccessToken();
    info('token=======$a');
  }

  @override
  Widget build(BuildContext context) {
    // token();
    // SystemChrome.setSystemUIOverlayStyle(
    //     const SystemUiOverlayStyle(statusBarColor: Colors.white));
    final textTheme = Theme.of(context).textTheme;
    // final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom != 0;
    final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom != 0;

    return MultiBlocListener(
        listeners: [
          BlocListener<NotificationCubit, NotificationState>(
              listener: (context, state) {
            info('state----------$state----}');
            if (state is NotificationError) {
              SnackBarService.error(
                context: context,
                message: state.message,
              );
            }
            if (state is NotificationCountLoaded) {
              info(
                  'state======-${state.notificationCountResponse.data?.count}');
              setState(() {
                counts = state.notificationCountResponse.data?.count ?? 0;
              });
            }
          }),
        ],
        child: BlocConsumer<BottomNavbarCubit, BottomNavbarState>(
            listener: (context, state) {
          if (state is BottomNavbarModeLoaded) {
            setState(() {
              mode = state.mode;
            });
          }
          if (state is SelectedItemLoaded) {
            setState(() {
              selectedItem = state.selected;
            });
          }
        }, builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              toolbarHeight: 0,
              elevation: 0,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark,
                systemNavigationBarColor: Colors.white,
              ),
            ),
            extendBodyBehindAppBar: false,
            resizeToAvoidBottomInset: false,
            backgroundColor: Colors.white,
            body: PageView(
              controller: homePageController,
              physics: const NeverScrollableScrollPhysics(),
              onPageChanged: (index) {
                context.read<NotificationCubit>().getNotificationCount();

                setState(() {
                  _selectedIndex = index;
                });
              },
              children: _pages,
            ),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
            floatingActionButton: isKeyboardOpen
                ? const SizedBox()
                : !(mode == 1 || mode == 2)
                    ? Container(
                        clipBehavior: Clip.hardEdge,
                        margin: EdgeInsets.fromLTRB(8, 0, 8, isIos ? 4 : 8),
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.all(
                            Radius.circular(36),
                          ),
                          color: Colors.transparent,
                        ),
                        height: isIos ? 72 : 64,
                        child: Theme(
                          data: Theme.of(context).copyWith(
                            splashColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                          ),
                          child: BottomNavigationBar(
                            currentIndex: _selectedIndex,
                            type: BottomNavigationBarType.fixed,
                            backgroundColor: AppColors.navy,
                            onTap: (val) {
                              setState(() {
                                _selectedIndex = val;
                              });
                              homePageController.jumpToPage(
                                val,
                                // duration: const Duration(milliseconds: 300),
                                // curve: Curves.easeInOut,
                              );
                              sl<MixpanelService>().trackButtonClick(
                                  'Bottom Navbar',
                                  properties: {
                                    'Page': _selectedIndex == 0
                                        ? 'Home'
                                        : _selectedIndex == 1
                                            ? 'Explore'
                                            : 'Profile'
                                  });
                            },
                            selectedFontSize: 12,
                            unselectedItemColor: Colors.white,
                            selectedItemColor: AppColors.coral,
                            selectedLabelStyle:
                                textTheme.ralewayRegular.copyWith(
                              fontSize: 12,
                            ),
                            unselectedLabelStyle:
                                textTheme.ralewayRegular.copyWith(
                              fontSize: 12,
                            ),
                            items: [
                              BottomNavigationBarItem(
                                icon: Padding(
                                  padding: const EdgeInsets.only(bottom: 4),
                                  child: _selectedIndex == 0
                                      ? Image.asset(
                                          color: AppColors.coral,
                                          AppAssets.homefilled,
                                          width: 20,
                                        )
                                      : Image.asset(
                                          color: _selectedIndex == 0
                                              ? AppColors.coral
                                              : Colors.white,
                                          AppAssets.homeicon,
                                          width: 18,
                                        ),
                                ),
                                label: 'Home',
                              ),
                              BottomNavigationBarItem(
                                icon: Padding(
                                  padding: const EdgeInsets.only(bottom: 2),
                                  child: _selectedIndex == 1
                                      ? Image.asset(
                                          color: AppColors.coral,
                                          AppAssets.explorefilled,
                                          width: 20,
                                        )
                                      : Image.asset(
                                          color: _selectedIndex == 1
                                              ? AppColors.coral
                                              : Colors.white,
                                          AppAssets.explorehomeicon,
                                          width: 20,
                                        ),
                                ),
                                label: 'Explore',
                              ),
                              BottomNavigationBarItem(
                                icon: counts == 0
                                    ? Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 2),
                                        child: _selectedIndex == 2
                                            ? Image.asset(
                                                color: AppColors.coral,
                                                AppAssets.profilefilled,
                                                width: 13,
                                              )
                                            : Image.asset(
                                                color: _selectedIndex == 2
                                                    ? AppColors.coral
                                                    : Colors.white,
                                                AppAssets.profilehomeicon,
                                                width: 16,
                                              ),
                                      )
                                    : Badge(
                                        label: Padding(
                                          padding:
                                              const EdgeInsets.only(bottom: 2),
                                          child: Text(
                                            counts.toString(),
                                            style: textTheme.ralewayRegular
                                                .copyWith(
                                                    color: Colors.white,
                                                    fontSize: 10),
                                          ),
                                        ),
                                        backgroundColor: Colors.red,
                                        smallSize: 8,
                                        offset: Offset(
                                          _selectedIndex == 2 ? 16 : 12,
                                          _selectedIndex == 2 ? -4 : -4,
                                        ),
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(bottom: 2),
                                          child: _selectedIndex == 2
                                              ? Image.asset(
                                                  color: AppColors.coral,
                                                  AppAssets.profilefilled,
                                                  width: 14,
                                                )
                                              : Image.asset(
                                                  color: _selectedIndex == 2
                                                      ? AppColors.coral
                                                      : Colors.white,
                                                  AppAssets.profile,
                                                  width: 20,
                                                ),
                                        ), // Adjust position as needed
                                      ),
                                label: 'Profile',
                              ),
                            ],
                          ),
                        ),
                      )
                    : const SizedBox(),
          );
        }));
  }
}
