import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/hour_based_message_utils.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/check_in_request.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/bottom_navbar/bottom_navbar_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/check_in/check_in_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/check_in_data/checkin_data_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/normal_feed/normal_feed_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/widgets/check_in_section.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/widgets/chip_list_view.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/widgets/exercise_card.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/widgets/mood_row.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/blocs/notification/notification_cubit.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/curved_separator.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';
import 'package:gotcha_mfg_app/core/extensions/mood_list_extensions.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/popup.dart';
import '../../../check-in/presentation/widgets/check_in_completed_widget.dart';
import '../../../exercise/presentation/pages/mixed_exercise_page.dart';
import '../../data/models/delete_request.dart';
import '../../data/models/emotions_response.dart' as emotion;
import '../widgets/continue_workout_list.dart';

@RoutePage()
class NormalFeedPage extends StatefulWidget {
  const NormalFeedPage({
    super.key,
    required this.homePageController,
  });

  final PageController homePageController;

  @override
  State<NormalFeedPage> createState() => _NormalFeedPageState();
}

class _NormalFeedPageState extends State<NormalFeedPage> {
  String? feedbackEmotion;
  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Normal Feed Page',
      properties: {'Code': 'screen_view.normal_feed_page'},
    );
    

    context.read<NormalFeedCubit>().getData();
    // context.read<PollCubit>().getGymPollMessage();
    WidgetsBinding.instance.addPostFrameCallback((_) => _setStatusBar());
  }

  int selectedItem = -1;
  String selectedItemId = '';

  int? mode = 0;
  bool? checkedInToday = false;
  bool? editComplete = false;
  bool? showMoodRow = false;
  bool isEditing = false;
  List<emotion.Type>? emotionsList;
  String? name;
  bool sample = false;

  void _setStatusBar() {
  }
  onref() {}

  String capitalizeFirstLetter(String text) {
    if (text.isEmpty) {
      return text;
    }
    if (text.length == 1) {
      return text.toUpperCase();
    }
    return text[0].toUpperCase() + text.substring(1);
  }

  LinearGradient getRandomGradient() {
    final random = Random();

    Color generateBrighterColor() {
      const int minBrightness = 128;

      int r = minBrightness + random.nextInt(256 - minBrightness);
      int g = minBrightness + random.nextInt(256 - minBrightness);
      int b = minBrightness + random.nextInt(256 - minBrightness);

      return Color.fromRGBO(r, g, b, 1.0);
    }

    Color color1 = generateBrighterColor();
    Color color2;

    do {
      color2 = generateBrighterColor();
    } while (_colorDifference(color1, color2) < 50);

    return LinearGradient(
      colors: [color1, color2],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  int _colorDifference(Color c1, Color c2) {
    return (c1.red - c2.red).abs() +
        (c1.green - c2.green).abs() +
        (c1.blue - c2.blue).abs();
  }

  bool isKeyboardOpen1(BuildContext context) {
    final viewInsets = MediaQuery.of(context).viewInsets;
    return viewInsets.bottom > 0;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom != 0;
    info("isKeyboardOpen ${MediaQuery.of(context).viewInsets.bottom}");

    return MultiBlocListener(
      listeners: [
        BlocListener<CheckInCubit, CheckInState>(listener: (context, state) {
          info('state----$state');
          if (state is DeleteSuccess) {
            context.read<NormalFeedCubit>().getData();
          }
          if (state is CheckInSuccess) {
            context.read<NotificationCubit>().getNotificationCount();


            // Show message sheet only if current hour is odd
            HourBasedMessageUtils.showMessageSheetIfOddHour(
              context: context,
              emotion: state.check.data?.emotion,
              name: name,
              gradient: getRandomGradient(),
              checkInCount: state.check.data?.checkInCount,
            );
            if (state.check.data?.notification != null) {
              CustomCupertinoAlertDialog.showAlertPopup(
                okButtonText: 'Ok',
                context,
                title: state.check.data?.notification?.title ?? 'N/A',
                content: state.check.data?.notification?.body ?? 'N/A',
                onOk: () {},
              );
              // Track notification delivery
              sl<MixpanelService>().trackPushNotificationDelivery(
                state.check.data?.notification?.type ?? 'N/A',
                'Foreground',
                properties: {
                  'Page': 'Normal Feed Page',
                  'In App Alert': true,
                },
              );
            }
            context.read<NormalFeedCubit>().getData();

            // Reset UI state after successful check-in
            setState(() {
              mode = 0;
              selectedItem = -1;
              selectedItemId = '';
              isEditing = false;
              showMoodRow = false;
            });
            context
                .read<BottomNavbarCubit>()
                .modeUpdate(0); // Update BottomNavbarCubit mode
            context
                .read<BottomNavbarCubit>()
                .itemUpdate(-1); // Update BottomNavbarCubit selected item
          }
        }),
      ],
      child: BlocConsumer<NormalFeedCubit, NormalFeedState>(
        listener: (context, state) {
          if (state is NormalFeedLoaded) {
            name = state.emotionsResponse?.data?.firstName ?? '';
            emotionsList = state.emotionsResponse?.data?.type?.sortByPoint();
            if (state.emotionsResponse?.data?.isCheckInToday == true) {
              context.read<BottomNavbarCubit>().refresh(emotionsList);
              mode = 0;
              context
                  .read<BottomNavbarCubit>()
                  .modeUpdate(0); // update mode in bottom nav bar cubit
              selectedItem = emotionsList?.indexWhere((element) =>
                      element.id ==
                      state.emotionsResponse?.data?.latestCheckInTypeId) ??
                  -1;
              context.read<BottomNavbarCubit>().itemUpdate(
                  selectedItem); // Update BottomNavbarCubit here as well
            } else {
              context.read<BottomNavbarCubit>().refresh(emotionsList);

              mode = 0;
              context
                  .read<BottomNavbarCubit>()
                  .modeUpdate(0); // update mode in bottom nav bar cubit

              selectedItem = -1;
              var currentEmotionId =
                  state.emotionsResponse?.data?.latestCheckInTypeId;
              final emotionIndex = emotionsList?.indexWhere(
                      (element) => element.id == currentEmotionId) ??
                  -1;
              if (emotionIndex != -1) {
                context.read<BottomNavbarCubit>().itemUpdate(emotionIndex);
                selectedItem = emotionIndex;
              } else {
                context.read<BottomNavbarCubit>().itemUpdate(
                    -1); // Ensure BottomNavbarCubit is updated when no emotion is selected
              }
            }
          }
        },
        builder: (context, state) {
          if (state is CheckInLoading) {
            return const LoadingWidget(color: Colors.white);
          }
          if (state is NormalFeedLoaded) {
            var firstName = state.emotionsResponse?.data?.firstName ?? '';
            var emotions = state.emotionsResponse?.data?.type?.sortByPoint();
            var exercises = state.exercisesResponse?.data;
            var isOnboarding =
                state.emotionsResponse?.data?.isOnboarding ?? false;
            var isCheckIn =
                state.emotionsResponse?.data?.isCheckInToday ?? false;
            checkedInToday = isCheckIn;
            var continueWorkout = state.continueWorkout?.data;
            bool hasWorkout = continueWorkout != null;
            return Scaffold(
                appBar: AppBar(
                  toolbarHeight: 0,
                  elevation: 0,
                  backgroundColor: Colors.transparent,
                  scrolledUnderElevation: 0,
                  systemOverlayStyle: const SystemUiOverlayStyle(
                    statusBarColor: Colors.transparent,
                    systemNavigationBarIconBrightness: Brightness.dark,
                    statusBarBrightness: Brightness.light,
                    statusBarIconBrightness: Brightness.dark,
                    systemNavigationBarColor: Colors.white,
                  ),
                ),
                extendBodyBehindAppBar: false,
                resizeToAvoidBottomInset: true,
                backgroundColor: Colors.white,
                body: SafeArea(
                  child: GestureDetector(
                    onTap: () {
                      FocusScope.of(context).unfocus();
                    },
                    behavior: HitTestBehavior.translucent,
                    child: Padding(
                      padding: EdgeInsets.only(
                        top: isIos ? 0 : 8,
                        right: 8,
                        left: 8,
                        bottom: (mode == 1 || mode == 2)
                            ? 0
                            : isIos
                                ? 32
                                : 32,
                      ),
                      child: RefreshIndicator(
                        color: AppColors.coral,
                        backgroundColor: Colors.white,
                        onRefresh: () async {
                          context.read<NormalFeedCubit>().getData();
                        },
                        child: Container(
                            decoration: const BoxDecoration(
                              color: AppColors.grey,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(30),
                                topRight: Radius.circular(30),
                              ),
                            ),
                            child: ListView(
                              children: [
                                GestureDetector(
                                
                                  onDoubleTap: () {
                                  
                                  },
                                  child: AppHeader(
                                    title: firstName.isNotEmpty
                                        ? 'Hi ${capitalizeFirstLetter(firstName)}!'
                                        : 'Welcome',
                                    subTitle: firstName.isNotEmpty
                                        ? 'Welcome back to the gym'
                                        : 'to the Mental Fitness Gym',
                                    showLogo: true,
                                  ),
                                  
                                ),
                                const CurvedSeparator(
                                  outerColor: AppColors.navy,
                                  innerColor: AppColors.grey,
                                ),
                                Container(
                                  decoration: const BoxDecoration(
                                    color: AppColors.grey,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      (checkedInToday == true &&
                                              mode == 0 &&
                                              !isEditing)
                                          ? CheckInCompleteView(
                                              description: emotions
                                                      ?.where(
                                                        (element) =>
                                                            element.name ==
                                                            state
                                                                .emotionsResponse
                                                                ?.data
                                                                ?.latestCheckInData
                                                                ?.feeling
                                                                ?.name,
                                                      )
                                                      .firstOrNull
                                                      ?.description ??
                                                  '',
                                              emotion: state
                                                      .emotionsResponse
                                                      ?.data
                                                      ?.latestCheckInData
                                                      ?.feeling
                                                      ?.name ??
                                                  '',
                                              dateTime: state.emotionsResponse
                                                  ?.data?.latestCheckInDate,
                                              onEdit: () {
                                                sl<MixpanelService>()
                                                    .trackButtonClick(
                                                        'Edit Check-In',
                                                        properties: {
                                                      'Page':
                                                          'Normal Feed Page',
                                                      'Code':
                                                          'click.edit_check_in'
                                                    });

                                                setState(() {
                                                  mode = 1;
                                                  showMoodRow = true;
                                                  isEditing = true;
                                                  selectedItem = findMainFeelingIndex(
                                                      emotionsList,
                                                      state
                                                              .emotionsResponse
                                                              ?.data
                                                              ?.latestCheckInTypeId ??
                                                          '');
                                                });
                                                context
                                                    .read<BottomNavbarCubit>()
                                                    .modeUpdate(1);
                                                context
                                                    .read<BottomNavbarCubit>()
                                                    .itemUpdate(
                                                        selectedItem); // Update BottomNavbarCubit on edit as well
                                                context
                                                    .read<CheckInCubit>()
                                                    .getDetailEmotions(
                                                      emotions?[selectedItem]
                                                              .id ??
                                                          '',
                                                    );
                                              },
                                            )
                                          : const SizedBox(),
                                      if (!(checkedInToday == true &&
                                          mode == 0 &&
                                          !isEditing))
                                        Padding(
                                          padding: const EdgeInsets.fromLTRB(
                                            24,
                                            6,
                                            24,
                                            16,
                                          ),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Expanded(
                                                    child: Text(
                                                      "Right now I'm feeling...",
                                                      style: textTheme
                                                          .ralewaySemiBold
                                                          .copyWith(
                                                        fontSize: 17,
                                                      ),
                                                    ),
                                                  ),
                                                  if (mode != 0)
                                                    Container(
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(30),
                                                        color:
                                                            AppColors.lightBlue,
                                                      ),
                                                      height: 24,
                                                      width: 24,
                                                      child: IconButton(
                                                        padding:
                                                            EdgeInsets.zero,
                                                        constraints:
                                                            const BoxConstraints(),
                                                        onPressed: () {
                                                          setState(() {
                                                            mode = 0;
                                                            isEditing = false;
                                                            selectedItem = -1;
                                                            context
                                                                .read<
                                                                    BottomNavbarCubit>()
                                                                .modeUpdate(
                                                                    0); // update mode in bottom nav bar cubit
                                                            context
                                                                .read<
                                                                    BottomNavbarCubit>()
                                                                .itemUpdate(-1);
                                                          });
                                                        },
                                                        icon: const Icon(
                                                          Icons.close,
                                                          color: AppColors.navy,
                                                          size: 16,
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                              const Gap(16),
                                              if (mode == 1 ||
                                                  mode == 2 ||
                                                  checkedInToday == false)
                                                MoodRow(
                                                  moods: emotions ?? [],
                                                  selectedItem: selectedItem,
                                                  onSelectedItemChanged:
                                                      (index) {
                                                    setState(() {
                                                      context
                                                          .read<
                                                              CheckinDataCubit>()
                                                          .clear();

                                                      context
                                                          .read<
                                                              BottomNavbarCubit>()
                                                          .itemUpdate(
                                                              index); // Already updating BottomNavbarCubit here
                                                      selectedItem = index;

                                                      if (selectedItem < 0) {
                                                        context
                                                            .read<
                                                                BottomNavbarCubit>()
                                                            .modeUpdate(
                                                                0); // update mode in bottom nav bar cubit
                                                        mode = 0;
                                                      } else {
                                                        context
                                                            .read<
                                                                BottomNavbarCubit>()
                                                            .modeUpdate(
                                                                1); // update mode in bottom nav bar cubit

                                                        mode = 1;
                                                        showMoodRow = true;
                                                        isEditing = true;
                                                      }
                                                    });
                                                    if (selectedItem >= 0) {
                                                      context
                                                          .read<
                                                              CheckinDataCubit>()
                                                          .clear();
                                                      context
                                                          .read<CheckInCubit>()
                                                          .getDetailEmotions(
                                                            emotions?[selectedItem]
                                                                    .id ??
                                                                '',
                                                          );
                                                    }
                                                  },
                                                )
                                            ],
                                          ),
                                        ),
                                      const Gap(16),
                                      if (mode == 0) ...[
                                        /// Continue your workout
                                        if (hasWorkout)
                                          const CurvedSeparator(
                                            outerColor: AppColors.grey,
                                            innerColor: AppColors.lightRed,
                                          ),
                                        if (hasWorkout)
                                          ContinueWorkoutList(
                                            continueWorkout: [continueWorkout],
                                            onTap: (workout) {
                                              context.pushRoute(
                                                WorkoutRoute(
                                                  seriesId:
                                                      workout.seriesId ?? "",
                                                ),
                                              );
                                              sl<MixpanelService>()
                                                  .trackButtonClick(
                                                      'Workout Tapped',
                                                      properties: {
                                                    'Page': 'Normal Feed Page',
                                                    'Code':
                                                        'click.normal_feed_page.workout_tapped',
                                                    'Workout Name':
                                                        workout.seriesTitle ??
                                                            ''
                                                  });
                                            },
                                          ),

                                        /// Exercises
                                        CurvedSeparator(
                                          outerColor: hasWorkout
                                              ? AppColors.lightRed
                                              : AppColors.grey,
                                          innerColor: AppColors.lightBlue,
                                        ),
                                        Container(
                                          decoration: const BoxDecoration(
                                            color: AppColors.lightBlue,
                                          ),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Padding(
                                                padding: EdgeInsets.fromLTRB(
                                                  26,
                                                  2,
                                                  24,
                                                  (exercises?.tag?.answers !=
                                                          null)
                                                      ? 8
                                                      : 8,
                                                ),
                                                child: Text(
                                                  'Exercises recommended for you',
                                                  style: textTheme
                                                      .ralewaySemiBold
                                                      .copyWith(
                                                    fontSize: 17,
                                                  ),
                                                ),
                                              ),
                                              if (exercises?.tag?.answers !=
                                                  null)
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 4),
                                                  child: ChipListView(
                                                      tag: exercises
                                                          ?.tag?.answers,
                                                      onAdd: () {
                                                        var existingCheckInId = state
                                                                .emotionsResponse
                                                                ?.data
                                                                ?.latestCheckInTypeId ??
                                                            state
                                                                .emotionsResponse
                                                                ?.data
                                                                ?.type?[0]
                                                                .name;

                                                        if (existingCheckInId !=
                                                            null) {
                                                          var emotion = emotions
                                                              ?.firstWhere((e) =>
                                                                  e.id ==
                                                                  existingCheckInId);

                                                          var index =
                                                              emotions?.indexOf(
                                                                  emotion!);

                                                          setState(() {
                                                            selectedItem =
                                                                index ?? 0;
                                                            mode = 1;
                                                            showMoodRow = true;
                                                            isEditing = true;

                                                            context
                                                                .read<
                                                                    BottomNavbarCubit>()
                                                                .modeUpdate(
                                                                    1); // update mode in bottom nav bar cubit
                                                            context
                                                                .read<
                                                                    BottomNavbarCubit>()
                                                                .itemUpdate(
                                                                    selectedItem); // Update BottomNavbarCubit on chip add as well
                                                          });
                                                          context
                                                              .read<
                                                                  CheckInCubit>()
                                                              .getDetailEmotions(
                                                                existingCheckInId,
                                                              );

                                                          sl<MixpanelService>()
                                                              .trackButtonClick(
                                                                  'Add Emotion',
                                                                  properties: {
                                                                'Page':
                                                                    'Normal Feed Page',
                                                                'Code':
                                                                    'click.add_emotion'
                                                              });
                                                        }
                                                      },
                                                      onDelete: (val) {
                                                        if (val.type ==
                                                            'feelings') {
                                                          CustomCupertinoAlertDialog
                                                              .yesOrNoPopup(
                                                            yesButtonText:
                                                                'New check-in',
                                                            noButtonText:
                                                                'Not now',
                                                            context,
                                                            title:
                                                                "Do a new check-in",
                                                            content:
                                                                "Complete a new check-in to get new exercise suggestions",
                                                            onYes: () {
                                                              setState(() {
                                                                mode = 1;
                                                                showMoodRow =
                                                                    true;
                                                                isEditing =
                                                                    true;
                                                                selectedItem = findMainFeelingIndex(
                                                                    emotionsList,
                                                                    state
                                                                            .emotionsResponse
                                                                            ?.data
                                                                            ?.latestCheckInTypeId ??
                                                                        '');
                                                              });
                                                              context
                                                                  .read<
                                                                      BottomNavbarCubit>()
                                                                  .modeUpdate(
                                                                      1);
                                                              context
                                                                  .read<
                                                                      BottomNavbarCubit>()
                                                                  .itemUpdate(
                                                                      selectedItem); // Update BottomNavbarCubit on edit as well
                                                              context
                                                                  .read<
                                                                      CheckInCubit>()
                                                                  .getDetailEmotions(
                                                                    emotions?[selectedItem]
                                                                            .id ??
                                                                        '',
                                                                  );
                                                              return;
                                                            },
                                                            onNo: () {
                                                              Navigator.pop(
                                                                  context);

                                                              return;
                                                            },
                                                          );
                                                        } else {
                                                          context
                                                              .read<
                                                                  CheckInCubit>()
                                                              .deleteCheckIn(
                                                                DeleteParams(
                                                                  dailyCheckInId:
                                                                      exercises
                                                                              ?.tag
                                                                              ?.dailyCheckInId ??
                                                                          '',
                                                                  emotionId:
                                                                      val.answerId ??
                                                                          '',
                                                                ),
                                                              );

                                                          setState(() {
                                                          });
                                                        }
                                                        // mixpanel
                                                        sl<MixpanelService>()
                                                            .trackButtonClick(
                                                                'Delete Emotion',
                                                                properties: {
                                                              'Page':
                                                                  'Normal Feed Page',
                                                              'Code':
                                                                  'click.delete_emotion',
                                                              'Emotion Name':
                                                                  val.name ??
                                                                      '',
                                                            });
                                                      }),
                                                ),
                                              ListView.separated(
                                                itemCount: exercises?.exercises
                                                        ?.where(
                                                            (e) => e != null)
                                                        .length ??
                                                    0,
                                                padding: EdgeInsets.zero,
                                                shrinkWrap: true,
                                                physics:
                                                    const NeverScrollableScrollPhysics(),
                                                itemBuilder: (context, index) {
                                                  var item = exercises
                                                      ?.exercises
                                                      ?.where((e) => e != null)
                                                      .toList()[index];
                                                  return Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                      horizontal: 24,
                                                    ),
                                                    child: ExerciseCard(
                                                      title:
                                                          item?.exerciseTitle ??
                                                              'N/A',
                                                      subtitle:
                                                          item?.exerciseType ??
                                                              'N/A',
                                                      duration:
                                                          item?.mediaDuration ??
                                                              'N/A',
                                                      tag: item?.categoryName ??
                                                          'N/A',
                                                      thumbnail:
                                                          item?.thumbnailUrl ??
                                                              'N/A',
                                                      onTap: () {

                                                        Navigator.of(context)
                                                            .push(
                                                          MaterialPageRoute(
                                                            builder: (context) =>
                                                                MixedExercisePage(
                                                              notification:
                                                                  false,
                                                              id: item?.exerciseId ??
                                                                  '',
                                                              seriesId: "",
                                                              isLast: false,
                                                              isFirst: false,
                                                              isOverride: false,
                                                            ),
                                                          ),
                                                        );
                                                        // mixpanel
                                                        sl<MixpanelService>()
                                                            .trackButtonClick(
                                                                'Exercise Tapped',
                                                                properties: {
                                                              'Page':
                                                                  'Normal Feed Page',
                                                              'Code':
                                                                  'click.normal_feed_page.exercise_tapped',
                                                              'Exercise Name':
                                                                  item?.exerciseTitle ??
                                                                      ''
                                                            });
                                                        return;
                                                      },
                                                    ),
                                                  );
                                                },
                                                separatorBuilder:
                                                    (context, index) {
                                                  return const Gap(12);
                                                },
                                              ),
                                              const Gap(32),
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 28),
                                                width: double.infinity,
                                                child: PrimaryButton(
                                                  text: 'Refresh suggestions',
                                                  onPressed: () {
                                                    sl<MixpanelService>()
                                                        .trackButtonClick(
                                                            'Refresh Suggestions',
                                                            properties: {
                                                          'Page':
                                                              'Normal Feed Page',
                                                          'Code':
                                                              'click.normal_feed_page.refresh_suggestions'
                                                        });

                                                    context
                                                        .read<NormalFeedCubit>()
                                                        .getData();
                                                  },
                                                ),
                                              ),
                                              const Gap(16),
                                              Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  horizontal: 28,
                                                  vertical: 20,
                                                ),
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              100.0),
                                                      border: Border.all(
                                                          width: .75,
                                                          color: AppColors
                                                              .midBlue)),
                                                ),
                                               
                                              ),
                                              const Gap(8),
                                            ],
                                          ),
                                        ),
                                        Container(
                                          decoration: const BoxDecoration(
                                            color: AppColors.lightBlue,
                                          ),
                                          padding: const EdgeInsets.only(
                                            left: 28,
                                            right: 28,
                                            bottom: 80,
                                          ),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Need more help?',
                                                style: textTheme.ralewaySemiBold
                                                    .copyWith(
                                                  fontSize: 17,
                                                ),
                                              ),
                                              const Gap(16),
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: PrimaryButtonVariant(
                                                      isProfessional: true,
                                                      text: 'Professional help',
                                                      onPressed: () {
                                                        sl<MixpanelService>()
                                                            .trackButtonClick(
                                                                'Professional Help',
                                                                properties: {
                                                              'Page':
                                                                  'Normal Feed Page',
                                                              'Code':
                                                                  'click.normal_feed_page.professional_help'
                                                            });

                                                        context.pushRoute(
                                                            const HelpSeekingPathwayRoute());
                                                      },
                                                    ),
                                                  ),
                                                  const Gap(16),
                                                  Expanded(
                                                    child: PrimaryButtonVariant(
                                                      isProfessional: false,
                                                      text: 'More exercises',
                                                      onPressed: () {
                                                        widget
                                                            .homePageController
                                                            .jumpToPage(1);
                                                        sl<MixpanelService>()
                                                            .trackButtonClick(
                                                                'More Exercises',
                                                                properties: {
                                                              'Page':
                                                                  'Normal Feed Page',
                                                              'Code':
                                                                  'click.normal_feed_page.more_exercises'
                                                            });
                                                      },
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const Gap(32),
                                            ],
                                          ),
                                        ),
                                      ],
                                      if (mode == 1)
                                        showMoodRow == true
                                            ? CheckIn(
                                                latestCheckInData: state
                                                    .emotionsResponse
                                                    ?.data
                                                    ?.latestCheckInData,
                                                selectedItem: selectedItem,
                                                checkInTypeId: (selectedItem >=
                                                        0)
                                                    ? (emotions?[selectedItem]
                                                        .id)
                                                    : null,
                                              )
                                            : const SizedBox(),
                                      if (mode == 2)
                                        Container(
                                          padding: const EdgeInsets.all(16),
                                          decoration: const BoxDecoration(
                                            borderRadius: BorderRadius.only(
                                              bottomLeft: Radius.circular(24),
                                              bottomRight: Radius.circular(24),
                                            ),
                                            color: AppColors.grey,
                                          ),
                                          height: size.height * 0.65,
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            )),
                      ),
                    ),
                  ),
                ),
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.centerDocked,
                floatingActionButton: isKeyboardOpen
                    ? const SizedBox()
                    : (mode == 1 || mode == 2)
                        ? Container(
                            width: double.infinity,
                            margin: const EdgeInsets.symmetric(
                              vertical: 12,
                              horizontal: 32,
                            ),
                            child: PrimaryButton(
                              text: 'Save',
                              // showShadowAbove: true && mode == 1,
                              isEnabled: mode == 1,
                              onPressed: () {
                                sl<MixpanelService>().trackButtonClick(
                                    'Save Check-In',
                                    properties: {'Code': 'click.save_checkin'});

                                if (mode != 1) return;
                                if (emotions?[selectedItem].id == null) return;
                                final checkInDataCubit =
                                    context.read<CheckinDataCubit>();
                                var checkInData =
                                    checkInDataCubit.state as CheckInDataLoaded;
                                var questionslist = checkInData.list;
                                var checkInRequest = CheckInRequest(
                                  questions: questionslist,
                                  checkInTypesId:
                                      emotions?[selectedItem].id ?? "",
                                  isOnboarding: false,
                                );
                                sl<MixpanelService>().trackCheckInWithOptions(
                                  'Chec In Page',
                                  questions: questionslist,
                                );
                                context
                                    .read<CheckInCubit>()
                                    .checkIn(checkInRequest);
                                context.read<CheckinDataCubit>().clear();
                              },
                            ),
                          )
                        : const SizedBox());
          } else if (state is NormalFeedLoading) {
            return _buildSkeletonLoader(context, textTheme);
          } else {
            return RetryWidget(
              onRetry: () {
                context.read<NormalFeedCubit>().getData();
              },
              color: Colors.white,
            );
          }
        },
      ),
    );
  }

  int findMainFeelingIndex(List<emotion.Type>? types, String typeId) {
    if (types == null) return -1;
    return types.indexWhere((type) => type.id == typeId);
  }

  Widget _buildSkeletonLoader(BuildContext context, TextTheme textTheme) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        backgroundColor: Colors.transparent,
        scrolledUnderElevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
        ),
      ),
      extendBodyBehindAppBar: false,
      resizeToAvoidBottomInset: true,
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.only(
            top: isIos ? 0 : 8,
            right: 8,
            left: 8,
            bottom: isIos ? 32 : 32,
          ),
          child: Skeletonizer(
            effect:  const ShimmerEffect(
              duration: Duration(milliseconds: 1000),
            ),
            
            enabled: true,
            enableSwitchAnimation: true,
            child: Container(
              decoration: const BoxDecoration(
                color: AppColors.grey,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
              ),
              child: ListView(
                children: [

                  Container(
                    width: double.infinity,
                    height: 120,
                    decoration: const BoxDecoration(
                      color: AppColors.navy,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  height: 24,
                                  width: 180,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.3),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                const Gap(8),
                               
                                  
                                   Container(
                                    height: 16,
                                    width: 220,
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                  ),
                                // ),
                              ],
                            ),
                          ),
                //
                          Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const CurvedSeparator(
                    outerColor: AppColors.navy,
                    innerColor: AppColors.grey,
                  ),
                  Container(
                    decoration: const BoxDecoration(
                      color: AppColors.grey,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [

                        Padding(
                          padding: const EdgeInsets.fromLTRB(24, 6, 24, 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                      "Right now I'm feeling...",
                                      style: textTheme.ralewaySemiBold.copyWith(
                                        fontSize: 17,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const Gap(16),

                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: List.generate(5, (index) =>
                                  Container(
                                    width: 50,
                                    height: 70,
                                    decoration: BoxDecoration(
                                      color: Colors.grey[300],
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                ),
                              ),
                              const Gap(10),
                               Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                      "Right now I'm feeling...",
                                      style: textTheme.ralewaySemiBold.copyWith(
                                        fontSize: 17,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const Gap(8),
                               Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: List.generate(3, (index) {
                                 return Padding(
                                   padding:  EdgeInsets.only(left: index==0?0:  8.0),
                                   child: Container(
                                      width: 90,
                                      height: 40,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[300],
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                    ),
                                 );
                                }
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Exercises section skeleton
                        const CurvedSeparator(
                          outerColor: AppColors.lightRed,
                          innerColor: AppColors.lightBlue,
                        ),
                        Container(
                          decoration: const BoxDecoration(
                            color: AppColors.lightBlue,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.fromLTRB(26, 2, 24, 4),
                                child: Text(
                                  'Exercises recommended for you',
                                  style: textTheme.ralewaySemiBold.copyWith(
                                    fontSize: 17,
                                  ),
                                ),
                              ),

                              ...List.generate(3, (index) =>
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 6,
                                  ),
                                  child: Container(
                                    width: double.infinity,
                                    height: 196,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.05),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Row(mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          // Thumbnail skeleton
                                          // Container(
                                          //   width: 108,
                                          //   height: 108,
                                          //   decoration: BoxDecoration(
                                          //     color: Colors.grey[300],
                                          //     borderRadius: BorderRadius.circular(12),
                                          //   ),
                                          // ),
                                          // const Gap(16),
                                          // Content skeleton
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              children: [
                                                const Gap(8),
                                                Container(
                                                      height: 20,
                                                      width: 80,
                                                      decoration: BoxDecoration(
                                                        color: Colors.grey[300],
                                                        borderRadius: BorderRadius.circular(10),
                                                      ),
                                                    ),
                                                    const Gap(8),
                                                    Spacer(),
                                                // Title skeleton
                                                Container(
                                                  height: 18,
                                                  width: double.infinity,
                                                  decoration: BoxDecoration(
                                                    color: Colors.grey[300],
                                                    borderRadius: BorderRadius.circular(4),
                                                  ),
                                                ),
                                                const Gap(8),
                                                // Subtitle skeleton
                                                Container(
                                                  height: 14,
                                                  width: 140,
                                                  decoration: BoxDecoration(
                                                    color: Colors.grey[300],
                                                    borderRadius: BorderRadius.circular(4),
                                                  ),
                                                ),
                                                const Gap(12),
                                                // Duration and tag row skeleton
                                                Row(
                                                  children: [
                                                    Container(
                                                      height: 12,
                                                      width: 60,
                                                      decoration: BoxDecoration(
                                                        color: Colors.grey[300],
                                                        borderRadius: BorderRadius.circular(4),
                                                      ),
                                                    ),
                                                    Spacer(),
                                                    Container(
                                                      height: 40,
                                                      width: 40,
                                                      decoration: BoxDecoration(
                                                        color: Colors.grey[300],
                                                        borderRadius: BorderRadius.circular(32),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const Gap(24),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
