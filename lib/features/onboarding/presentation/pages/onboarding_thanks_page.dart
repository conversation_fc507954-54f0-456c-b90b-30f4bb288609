import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/hour_based_message_utils.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';

@RoutePage()
class OnBoardingThanksPage extends StatefulWidget {
  final EmotionsDetailResponse? data;
  final String? description;
  final String checkInTypeId;

  const OnBoardingThanksPage({
    super.key,
    required this.data,
    this.description,
    required this.checkInTypeId,
  });

  @override
  State<OnBoardingThanksPage> createState() => _OnBoardingThanksPageState();
}

class _OnBoardingThanksPageState extends State<OnBoardingThanksPage> {
  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Onboarding Thanks Page',
      properties: {'Code': 'screen_view.onboarding_thanks_page'},
    );

    // Set flag to show message sheet when user eventually reaches home page
    HourBasedMessageUtils.setShouldShowMessageSheet(true);
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: AppColors.lightBlue,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(
          vertical: 24,
          horizontal: 32,
        ),
        child: PrimaryButton(
          text: 'Next',
          isEnabled: true,
          onPressed: () {
            sl<MixpanelService>().trackButtonClick('Next', properties: {
              'Page': 'Onboarding Thanks Page',
              'Code': 'click.onboarding_thanks_page.next'
            });
            context.pushRoute(
              OnboardingDealingWithRoute(
                data: widget.data,
                checkInTypeId: widget.checkInTypeId,
              ),
            );
          },
        ),
      ),
      body: Padding(
        padding: EdgeInsets.only(
          top: isIos ? 4 : 8,
          left: 8,
          right: 8,
        ),
        child: Column(
          children: [
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                  color: AppColors.lightBlue,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Gap(24),
                    Text(
                      "You've almost completed your first check-in!",
                      style: textTheme.sectionHeading,
                    ),
                    const Gap(24),
                    Text(
                      '${widget.description}',
                      style: textTheme.bodyRegular,
                    ),
                    const Gap(48),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
