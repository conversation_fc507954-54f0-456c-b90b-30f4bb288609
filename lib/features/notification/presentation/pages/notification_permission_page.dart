import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/hour_based_message_utils.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io' show Platform;

import '../../../../core/mixpanel_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/primary_button.dart';

@RoutePage()
class NotificationPermissionPage extends StatefulWidget {
  const NotificationPermissionPage({super.key});

  @override
  State<NotificationPermissionPage> createState() =>
      _NotificationPermissionPageState();
}

class _NotificationPermissionPageState
    extends State<NotificationPermissionPage> {
  Future<void> requestPermission() async {
    // For iOS, we can't rely on Permission.notification.status
    // as it may incorrectly return denied even when permission is granted
    if (Platform.isIOS) {
      // Use iOS-specific permission check
      bool iosPermissionGranted = await requestPermissionsIOS();
      info('iosPermissionGranted----$iosPermissionGranted');

      if (iosPermissionGranted) {
        sl<MixpanelService>()
            .trackNotificationPermission(true, 'iOS', properties: {
          'Page': 'Notification Permission Page',
          'Code': 'event.notification_permission_page.granted'
        });
        // Permission is granted on iOS, navigate to home
        if (mounted) {
          context.router.replaceAll(
            [HomeRoute(index: 0)],
            updateExistingRoutes: false,
          );
          // Show message sheet if current hour is odd (after navigation)
          _scheduleMessageSheetCheck();
        }
        return;
      } else {
        // iOS permissions were denied, show message and navigate
        sl<MixpanelService>()
            .trackNotificationPermission(false, 'iOS', properties: {
          'Page': 'Notification Permission Page',
          'Code': 'event.notification_permission_page.denied'
        });
        if (mounted) {
          SnackBarService.error(
            context: context,
            message:
                'Notifications are disabled. You can enable them in settings later.',
          );
          context.router.replaceAll(
            [HomeRoute(index: 0)],
            updateExistingRoutes: false,
          );
          _scheduleMessageSheetCheck();
        }
        return;
      }
    }

    // For Android, we can use the permission_handler approach
    else {
      final status = await Permission.notification.status;

      // If permission is already granted, go straight to home
      if (status.isGranted) {
        sl<MixpanelService>()
            .trackNotificationPermission(true, 'Android', properties: {
          'Status': 'Already Granted',
          'Page': 'Notification Permission Page',
          'Code': 'event.notification_permission_page.already_granted'
        });
        if (mounted) {
          context.router.replaceAll(
            [HomeRoute(index: 0)],
            updateExistingRoutes: false,
          );
          _scheduleMessageSheetCheck();
        }
        return;
      }

      // Request permission through permission_handler
      final requestStatus = await Permission.notification.request();

      if (requestStatus.isGranted) {
        // Permission granted, navigate to home
        sl<MixpanelService>()
            .trackNotificationPermission(true, 'Android', properties: {
          'Page': 'Notification Permission Page',
          'Code': 'event.notification_permission_page.granted'
        });
        if (mounted) {
          context.router.replaceAll(
            [HomeRoute(index: 0)],
            updateExistingRoutes: false,
          );
          _scheduleMessageSheetCheck();
        }
      } else if (requestStatus.isDenied || requestStatus.isPermanentlyDenied) {
        // Permission denied, still navigate to home but show a message
        sl<MixpanelService>()
            .trackNotificationPermission(false, 'Android', properties: {
          'Status': requestStatus.isPermanentlyDenied
              ? 'Permanently Denied'
              : 'Denied',
          'Page': 'Notification Permission Page',
          'Code': requestStatus.isPermanentlyDenied
              ? 'event.notification_permission_page.permanently_denied'
              : 'event.notification_permission_page.denied'
        });
        if (mounted) {
          SnackBarService.error(
            context: context,
            message:
                'Notifications are disabled. You can enable them in settings later.',
          );
          context.router.replaceAll(
            [HomeRoute(index: 0)],
            updateExistingRoutes: false,
          );
          _scheduleMessageSheetCheck();
        }
      }
    }
  }

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Notification Permission Page',
      properties: {
        'Code': 'screen_view.notification_permission_page',
        'Timestamp': DateTime.now().toIso8601String(),
        'Platform': Platform.isIOS ? 'iOS' : 'Android'
      },
    );
  }

  void _scheduleMessageSheetCheck() {
    // Set flag to show message sheet on next home page load
    HourBasedMessageUtils.setShouldShowMessageSheet(true);
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;
    return Scaffold(
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        child: Container(
          width: size.width,
          margin: const EdgeInsets.symmetric(
            vertical: 8,
            horizontal: 4,
          ),
          child: PrimaryButton(
            text: 'Next',
            onPressed: () {
              sl<MixpanelService>().trackButtonClick('Next', properties: {
                'Page': 'Notification Permission Page',
                'Code': 'click.notification_permission_page.next',
                'Platform': Platform.isIOS ? 'iOS' : 'Android'
              });
              requestPermission();
            },
          ),
        ),
      ),
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          systemNavigationBarColor: AppColors.grey,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      ),
      body: SafeArea(
        top: true,
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.only(
            top: 8,
            left: 8,
            right: 8,
          ),
          child: Column(
            children: [
              Expanded(
                child: Container(
                  width: size.width,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                    color: AppColors.grey,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: <Widget>[
                        const Spacer(
                          flex: 2,
                        ),
                        Text(
                          "We'll remind you tomorrow and send the occasional mental fitness tip!",
                          style: textTheme.bodyEmphasis,
                          textAlign: TextAlign.center,
                        ),
                        const Gap(64),
                        Image.asset(
                          AppAssets.permissionrequest,
                          scale: 2,
                        ),
                        const Spacer(
                          flex: 5,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Future<bool> requestPermissionsIOS() async {
  if (Platform.isIOS) {
    bool? result = await FlutterLocalNotificationsPlugin()
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
    // Return the result, defaulting to false if null
    return result ?? false;
  }
  // If not iOS, return true to continue with normal flow
  return true;
}
