import 'dart:math';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:gotcha_mfg_app/core/mixpanel_service.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/features/poll/presentation/pages/poll_concept.dart';
import 'package:gotcha_mfg_app/locator.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Utility class for handling hour-based message sheet display
class HourBasedMessageUtils {
  static const String _shouldShowMessageSheetKey = 'should_show_message_sheet';

  /// Sets a flag to show message sheet on next home page load
  static Future<void> setShouldShowMessageSheet(bool shouldShow) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_shouldShowMessageSheetKey, shouldShow);
  }

  /// Checks if message sheet should be shown and clears the flag
  static Future<bool> checkAndClearShouldShowMessageSheet() async {
    final prefs = await SharedPreferences.getInstance();
    final shouldShow = prefs.getBool(_shouldShowMessageSheetKey) ?? false;
    if (shouldShow) {
      await prefs.remove(_shouldShowMessageSheetKey);
    }
    return shouldShow;
  }


  /// Shows message sheet for navigation scenarios (from onboarding/notification pages)
  /// This method checks the flag and shows the message sheet if conditions are met
  static Future<void> showMessageSheetForNavigation(BuildContext context) async {
    final shouldShow = await checkAndClearShouldShowMessageSheet();
    if (shouldShow && isCurrentHourOdd()) {
      // Delay to ensure UI is ready
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (context.mounted) {
          sl<MixpanelService>().trackScreenView(
            'Gym Poll Page',
            properties: {'Code': 'screen_view.gym_poll_page'},
          );

          showMessageSheet(
            context,
            'positive', // Default emotion for navigation scenarios
            'User', // Default name
            _getRandomGradient(),
          );
        }
      });
    }
  }

  /// Generates a random gradient for the message sheet
  static LinearGradient _getRandomGradient() {
    final random = Random();

    Color generateBrighterColor() {
      const int minBrightness = 128;

      int r = minBrightness + random.nextInt(256 - minBrightness);
      int g = minBrightness + random.nextInt(256 - minBrightness);
      int b = minBrightness + random.nextInt(256 - minBrightness);

      return Color.fromRGBO(r, g, b, 1.0);
    }

    Color color1 = generateBrighterColor();
    Color color2;

    do {
      color2 = generateBrighterColor();
    } while (_colorDifference(color1, color2) < 50);

    return LinearGradient(
      colors: [color1, color2],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  /// Calculates the difference between two colors
  static int _colorDifference(Color c1, Color c2) {
    return (c1.red - c2.red).abs() +
        (c1.green - c2.green).abs() +
        (c1.blue - c2.blue).abs();
  }
}
